{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=AppointmentPlannerDb;Username=postgres;Password=****;Port=5432"}, "Jwt": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForSecurity!", "Issuer": "AppointmentPlanner", "Audience": "AppointmentPlannerUsers", "ExpirationMinutes": "1440"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*"}