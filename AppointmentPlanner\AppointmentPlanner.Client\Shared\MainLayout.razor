﻿@inherits LayoutComponentBase

@inject NavigationManager UriHelper
@using AppointmentPlanner.Data;
@using AppointmentPlanner.Models;
@using AppointmentPlanner.Client.Components;
@using Microsoft.AspNetCore.Components.Authorization;
@using Microsoft.JSInterop;
@inject IJSRuntime JSRuntime;

<div class="sb-content-overlay @(hide)">
    <div class="sb-loading">
        <svg class="circular" height="40" width="40">
            <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="6" stroke-miterlimit="10" />
        </svg>
    </div>
    <div class="sb-caption">
        <h4 class="my-4">Appointment planner initializing...</h4>
    </div>
</div>
<div class="planner-wrapper" style="width: 100%; visibility:@(visible); opacity:@(opacity)">
    <div class="planner-header">
        <SfAppBar ColorMode="AppBarColor.Primary" CssClass="custom-appbar">
            <div class="side-bar-opener">
                <span class="open-icon e-icons" @onclick="@showSideBar"></span>
            </div>
            <div class="name-cantainer">
                <span class="clinic-image icon-logo"></span>
                <h1 class="clinic-name appointment-title">APPOINTMENT PLANNER</h1>
            </div>
            <AppBarSpacer></AppBarSpacer>
            <AuthenticationStatus />
            <div class="sb-header-item sb-table-cell sb-download-wrapper">
                <a href="https://github.com/syncfusion/ej2-showcase-blazor-appointment-planner" target="_blank">
                    <div id="github" class="sb-github-btn">
                        <div class="github-image">
                            <img src="css/appoinment/mark.svg" alt="github" />
                        </div>
                    </div>
                </a>
            </div>
        </SfAppBar>
    </div>
    <SfSidebar ID="plannerSiderBar" EnableGestures="false" @bind-IsOpen="open" MediaQuery="(min-width: 600px)" Type="SidebarType.Auto" Created="OnCreate" CloseOnDocumentClick="onDocumentClick">
        <ChildContent>
            <div class="dock">
                <AuthorizeView>
                    <Authorized>
                        <div class="info align-center">
                            <div class="image"><img src="css/appoinment/assets/images/Admin.png" alt="Admin" /></div>
                            <div class="content nameContent">
                                <p class='name'>@context.User.Identity?.Name</p>
                                <p class='user-type'>@GetUserRole(context)</p>
                            </div>
                        </div>
                    </Authorized>
                    <NotAuthorized>
                        <div class="info align-center">
                            <div class="image"><img src="css/appoinment/assets/images/Admin.png" alt="Guest" /></div>
                            <div class="content nameContent">
                                <p class='name'>Guest User</p>
                                <p class='user-type'>Not Signed In</p>
                            </div>
                        </div>
                    </NotAuthorized>
                </AuthorizeView>
                <div class="sideparent menulist">
                    <ul class="nav flex-column">
                        <li class="nav-item e-list-item">
                            <NavLink class="nav-link sidebar-item" href="" Match="NavLinkMatch.All" @onclick="@CloseSidePane">
                                <span class="dashboard-image">
                                    <span class="item-image icon-dashboard"></span>
                                </span>
                                <span class="text" aria-hidden="true" title="Dashboard">Dashboard</span>
                            </NavLink>
                        </li>
                        <li class="nav-item e-list-item">
                            <NavLink class="nav-link sidebar-item" href="schedule" @onclick="@CloseSidePane">
                                <span class="dashboard-image">
                                    <span class="item-image icon-schedule"></span>
                                </span>
                                <span class="text" aria-hidden="true" title="Schedule">Schedule</span>
                            </NavLink>
                        </li>
                        <li class="nav-item e-list-item">
                            <NavLink class="nav-link sidebar-item" href="doctors" @onclick="@CloseSidePane">
                                <span class="dashboard-image">
                                    <span class="item-image icon-doctors"></span>
                                </span>
                                <span class="text" aria-hidden="true" title="Doctors">Doctors</span>
                            </NavLink>
                        </li>
                        <li class="nav-item e-list-item">
                            <NavLink class="nav-link sidebar-item" href="patients" @onclick="@CloseSidePane">
                                <span class="dashboard-image">
                                    <span class="item-image icon-patients"></span>
                                </span>
                                <span class="text" aria-hidden="true" title="Patients">Patients</span>
                            </NavLink>
                        </li>
                        <li class="nav-item e-list-item">
                            <NavLink class="nav-link sidebar-item" href="preference" @onclick="@CloseSidePane">
                                <span class="dashboard-image">
                                    <span class="item-image icon-preference"></span>
                                </span>
                                <span class="text" aria-hidden="true" title="Preference">Preference</span>
                            </NavLink>
                        </li>
                        <li class="nav-item e-list-item">
                            <NavLink class="nav-link sidebar-item" href="about" @onclick="@CloseSidePane">
                                <span class="dashboard-image">
                                    <span class="item-image icon-about"></span>
                                </span>
                                <span class="text" aria-hidden="true" title="About">About</span>
                            </NavLink>
                        </li>
                    </ul>
                </div>
            </div>
        </ChildContent>
    </SfSidebar>
    <main class="main-content">
        @Body
    </main>
</div>

@code{
    [Inject]
    protected AppointmentService Service { get; set; }
    [Inject]
    protected IJSRuntime JsRuntime { get; set; }

    private bool onDocumentClick { get; set; }
    private bool isDevice { get; set; }

    private string target { get; set; } = "#loader-content";

    private string visible { get; set; } = "hidden";
    private string OverlayClass { get; set; }

    private string hide { get; set; }
    private string opacity { get; set; } = "0";
    private bool open { get; set; }
    private List<NavigationMenu> navigationItems { get; set; }

    private void CloseSidePane()
    {
        if(this.isDevice){
            open = false;
        }
    }

    public void OnCreate()
    {
        this.hide = "sb-hide";
        this.visible = "visible";
        this.opacity = "1";
    }

    protected override void OnInitialized()
    {
        this.navigationItems = Service.NavigationMenu;
    }

    protected async override Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            this.isDevice = await JSRuntime.InvokeAsync<bool>("checkBrowserDevice");
            this.onDocumentClick = this.isDevice;
        }
    }

    private void showSideBar()
    {
        open = true;
    }

    private string GetUserRole(AuthenticationState authState)
    {
        var roles = authState.User.Claims
            .Where(c => c.Type == System.Security.Claims.ClaimTypes.Role)
            .Select(c => c.Value)
            .ToList();

        if (roles.Contains("Admin"))
            return "Administrator";
        else if (roles.Contains("Professional"))
            return "Healthcare Professional";
        else if (roles.Contains("Client"))
            return "Patient";

        return "User";
    }
}