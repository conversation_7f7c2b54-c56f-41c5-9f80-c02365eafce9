@page "/forgot-password"
@using AppointmentPlanner.Client.Models
@using AppointmentPlanner.Client.Services
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation

<div class="forgot-password-container">
    <div class="forgot-password-card">
        <div class="forgot-password-header">
            <h2>Reset Password</h2>
            <p>Enter your email address and we'll send you a link to reset your password</p>
        </div>

        @if (!emailSent)
        {
            <EditForm Model="@forgotPasswordModel" OnValidSubmit="@HandleForgotPassword">
                <DataAnnotationsValidator />
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <InputText id="email" @bind-Value="forgotPasswordModel.Email" class="form-control" placeholder="Enter your email address" />
                    <ValidationMessage For="@(() => forgotPasswordModel.Email)" />
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger">
                        @errorMessage
                    </div>
                }

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span>Sending...</span>
                        }
                        else
                        {
                            <span>Send Reset Link</span>
                        }
                    </button>
                </div>
            </EditForm>
        }
        else
        {
            <div class="success-message">
                <div class="success-icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22,4 12,14.01 9,11.01"></polyline>
                    </svg>
                </div>
                <h3>Check Your Email</h3>
                <p>We've sent a password reset link to <strong>@forgotPasswordModel.Email</strong></p>
                <p>Please check your email and follow the instructions to reset your password.</p>
                
                <div class="resend-section">
                    <p>Didn't receive the email?</p>
                    <button type="button" class="btn btn-outline" @onclick="@(() => emailSent = false)">
                        Try Again
                    </button>
                </div>
            </div>
        }

        <div class="forgot-password-footer">
            <p>
                Remember your password? <a href="/login">Sign in here</a>
            </p>
        </div>
    </div>
</div>

<style>
    .forgot-password-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .forgot-password-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 450px;
    }

    .forgot-password-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .forgot-password-header h2 {
        color: #333;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .forgot-password-header p {
        color: #666;
        margin: 0;
        line-height: 1.5;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 12px;
        border: 2px solid #e1e5e9;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .btn-outline {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .btn-outline:hover {
        background: #667eea;
        color: white;
    }

    .btn-block {
        width: 100%;
    }

    .alert {
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .success-message {
        text-align: center;
        padding: 20px 0;
    }

    .success-icon {
        color: #28a745;
        margin-bottom: 20px;
    }

    .success-message h3 {
        color: #333;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .success-message p {
        color: #666;
        margin-bottom: 15px;
        line-height: 1.5;
    }

    .resend-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e1e5e9;
    }

    .resend-section p {
        margin-bottom: 15px;
        font-size: 14px;
    }

    .forgot-password-footer {
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e1e5e9;
    }

    .forgot-password-footer p {
        margin: 10px 0;
    }

    .forgot-password-footer a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
    }

    .forgot-password-footer a:hover {
        text-decoration: underline;
    }

    .spinner-border {
        width: 1rem;
        height: 1rem;
        border: 0.125em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border 0.75s linear infinite;
    }

    .spinner-border-sm {
        width: 0.875rem;
        height: 0.875rem;
        border-width: 0.125em;
    }

    @keyframes spinner-border {
        to {
            transform: rotate(360deg);
        }
    }
</style>

@code {
    private ForgotPasswordDto forgotPasswordModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;
    private bool emailSent = false;

    private async Task HandleForgotPassword()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;

            var result = await AuthService.ForgotPasswordAsync(forgotPasswordModel);

            if (result.IsSuccess)
            {
                emailSent = true;
            }
            else
            {
                errorMessage = result.Message;
                if (result.Errors.Any())
                {
                    errorMessage += " " + string.Join(" ", result.Errors);
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An unexpected error occurred. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }
}
