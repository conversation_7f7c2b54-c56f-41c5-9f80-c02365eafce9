﻿<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(Program).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(Shared.MainLayout)">
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <div class="login-redirect">
                            <p>Please sign in to access this page.</p>
                            <a href="/login" class="btn btn-primary">Sign In</a>
                        </div>
                    }
                    else
                    {
                        <div class="unauthorized-container">
                            <h3>Access Denied</h3>
                            <p>You don't have permission to access this page.</p>
                            <a href="/" class="btn btn-primary">Go to Dashboard</a>
                        </div>
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Not found</PageTitle>
            <LayoutView Layout="@typeof(Shared.MainLayout)">
                <p role="alert">Sorry, there's nothing at this address.</p>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>


