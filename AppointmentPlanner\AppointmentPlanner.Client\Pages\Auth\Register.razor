@page "/register"
@using AppointmentPlanner.Client.Models
@using AppointmentPlanner.Client.Services
@using Microsoft.AspNetCore.Components.Authorization
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider

<div class="register-container">
    <div class="register-card">
        <div class="register-header">
            <h2>Create Account</h2>
            <p>Join Appointment Planner today</p>
        </div>

        <EditForm Model="@registerModel" OnValidSubmit="@HandleRegister">
            <DataAnnotationsValidator />
            
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName">First Name</label>
                    <InputText id="firstName" @bind-Value="registerModel.FirstName" class="form-control" placeholder="Enter your first name" />
                    <ValidationMessage For="@(() => registerModel.FirstName)" />
                </div>

                <div class="form-group">
                    <label for="lastName">Last Name</label>
                    <InputText id="lastName" @bind-Value="registerModel.LastName" class="form-control" placeholder="Enter your last name" />
                    <ValidationMessage For="@(() => registerModel.LastName)" />
                </div>
            </div>

            <div class="form-group">
                <label for="email">Email Address</label>
                <InputText id="email" @bind-Value="registerModel.Email" class="form-control" placeholder="Enter your email" />
                <ValidationMessage For="@(() => registerModel.Email)" />
            </div>

            <div class="form-group">
                <label for="phoneNumber">Phone Number (Optional)</label>
                <InputText id="phoneNumber" @bind-Value="registerModel.PhoneNumber" class="form-control" placeholder="Enter your phone number" />
                <ValidationMessage For="@(() => registerModel.PhoneNumber)" />
            </div>

            <div class="form-group">
                <label for="role">Account Type</label>
                <InputSelect id="role" @bind-Value="registerModel.Role" class="form-control">
                    <option value="@UserRoles.Client">Patient/Client</option>
                    <option value="@UserRoles.Professional">Healthcare Professional</option>
                </InputSelect>
                <ValidationMessage For="@(() => registerModel.Role)" />
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <InputText id="password" @bind-Value="registerModel.Password" type="password" class="form-control" placeholder="Enter your password" />
                <ValidationMessage For="@(() => registerModel.Password)" />
            </div>

            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <InputText id="confirmPassword" @bind-Value="registerModel.ConfirmPassword" type="password" class="form-control" placeholder="Confirm your password" />
                <ValidationMessage For="@(() => registerModel.ConfirmPassword)" />
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger">
                    @errorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success">
                    @successMessage
                </div>
            }

            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block" disabled="@isLoading">
                    @if (isLoading)
                    {
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        <span>Creating Account...</span>
                    }
                    else
                    {
                        <span>Create Account</span>
                    }
                </button>
            </div>
        </EditForm>

        <div class="register-footer">
            <p>
                Already have an account? <a href="/login">Sign in here</a>
            </p>
        </div>
    </div>
</div>

<style>
    .register-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .register-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 500px;
    }

    .register-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .register-header h2 {
        color: #333;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .register-header p {
        color: #666;
        margin: 0;
    }

    .form-row {
        display: flex;
        gap: 15px;
    }

    .form-row .form-group {
        flex: 1;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 12px;
        border: 2px solid #e1e5e9;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .btn-block {
        width: 100%;
    }

    .alert {
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .register-footer {
        text-align: center;
        margin-top: 30px;
    }

    .register-footer p {
        margin: 10px 0;
    }

    .register-footer a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
    }

    .register-footer a:hover {
        text-decoration: underline;
    }

    .spinner-border {
        width: 1rem;
        height: 1rem;
        border: 0.125em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border 0.75s linear infinite;
    }

    .spinner-border-sm {
        width: 0.875rem;
        height: 0.875rem;
        border-width: 0.125em;
    }

    @keyframes spinner-border {
        to {
            transform: rotate(360deg);
        }
    }

    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
            gap: 0;
        }
    }
</style>

@code {
    private RegisterDto registerModel = new() { Role = UserRoles.Client };
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleRegister()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            var result = await AuthService.RegisterAsync(registerModel);

            if (result.IsSuccess)
            {
                successMessage = "Account created successfully! Redirecting...";
                
                // Wait a moment to show success message
                await Task.Delay(1500);
                
                // Redirect to appropriate page based on role
                if (registerModel.Role == UserRoles.Professional)
                {
                    Navigation.NavigateTo("/professional");
                }
                else
                {
                    Navigation.NavigateTo("/");
                }
            }
            else
            {
                errorMessage = result.Message;
                if (result.Errors.Any())
                {
                    errorMessage += " " + string.Join(" ", result.Errors);
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An unexpected error occurred. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }
}
