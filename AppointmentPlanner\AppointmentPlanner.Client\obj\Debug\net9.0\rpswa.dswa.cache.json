{"GlobalPropertiesHash": "3v3PhFHxa3RPx/xEeD1iwtlAnXCJ3LIdGIeIX1ErbN0=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Tetx7K/G6HNvYY0qGru6zJbdj2pYvGa0HJG0F5MzPqM=", "bY3TtkrwtO+XTdprzUAfv2yC0LcFoBLabc/TY7lypes=", "kmhwCmQpTim+fBZ0W+Tzs+4uGLEtkC4vquOn7kiQr/c=", "S9smC7EJWS0XNSK41P94G+YZUzGzV29wqlVTD8Fxz+8=", "zwBGixs7n5it8n01HCX/joHXbncj2QUclhhyQOQVyiw=", "Lvso4JRZratSPVF16IA3AxzRO8Xsr65NCjAnavvN2eo=", "HKPyUcvF//VvP52hZxMYw7vd2k4Ry6iOyo5qXungnDc=", "JGp3PBOk2m7n+YjzAs19PkGPkPCFf/7OGkzFMsTQLJA=", "WM1oNafqJxpmckpm4yZVST7pZnmydYnpazahVFd9ddQ=", "tK8+c6MDUHcfLoV+9c2dRanuJZ8n2gKxuufMoMHhewA=", "cvNercnpEMQpGKHddY2xeAIMqnr7bfF45oPRW18p0R4=", "a++KxigcrPN5xY7bc1OUjGRlkMrFfXexwj1OENCgREg=", "Dxg9Rso1zqCL4EQm1xkg9IqaMosyT0BNaYl+BD1e9Eg=", "ylmAjBw5ZjYjdU2sshHZBZa7g306DVTPeIC/CSTJCcQ=", "NfeIsYgM2LJzMqU/ZERA3/YSil5yn0rlRjRO8yfe2HA=", "fCDFCWIUIgsg3zo5YFwHlUbwrkpNYt/yLHgpDFxl8so=", "t+5tOOK8746DiCnoOgcm2tuNUAt65v8nfzbS3yEdi1I=", "HiJkXRnXJbuPzdKepLpnKgugeAN5jFRm0kqleWSvjTs=", "Ao+CAvP/pIfw65kkwyu1s+ishISd9vKuaZt80kixlAA=", "kN+e5h8si/R3YHHO8l5XehKddaHMdrv7bLqBzfEukKk="], "CachedAssets": {"Tetx7K/G6HNvYY0qGru6zJbdj2pYvGa0HJG0F5MzPqM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Appointmeent wasm\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\appsettings.Development.json", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Appointmeent wasm\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0ueugt8gp", "Integrity": "gX2wvy7Mp4NkxB2695Sb8lBM9HocPQ1U876BeP78Aws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.Development.json", "FileLength": 119, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "bY3TtkrwtO+XTdprzUAfv2yC0LcFoBLabc/TY7lypes=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Appointmeent wasm\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\appsettings.json", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Appointmeent wasm\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0ueugt8gp", "Integrity": "gX2wvy7Mp4NkxB2695Sb8lBM9HocPQ1U876BeP78Aws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.json", "FileLength": 119, "LastWriteTime": "2025-06-27T04:50:09+00:00"}}, "CachedCopyCandidates": {}}