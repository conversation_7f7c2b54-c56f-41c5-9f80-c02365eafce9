{"GlobalPropertiesHash": "3v3PhFHxa3RPx/xEeD1iwtlAnXCJ3LIdGIeIX1ErbN0=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Tetx7K/G6HNvYY0qGru6zJbdj2pYvGa0HJG0F5MzPqM=", "bY3TtkrwtO+XTdprzUAfv2yC0LcFoBLabc/TY7lypes=", "kmhwCmQpTim+fBZ0W+Tzs+4uGLEtkC4vquOn7kiQr/c=", "Lvso4JRZratSPVF16IA3AxzRO8Xsr65NCjAnavvN2eo=", "a++KxigcrPN5xY7bc1OUjGRlkMrFfXexwj1OENCgREg=", "Dxg9Rso1zqCL4EQm1xkg9IqaMosyT0BNaYl+BD1e9Eg=", "ylmAjBw5ZjYjdU2sshHZBZa7g306DVTPeIC/CSTJCcQ=", "NfeIsYgM2LJzMqU/ZERA3/YSil5yn0rlRjRO8yfe2HA=", "fCDFCWIUIgsg3zo5YFwHlUbwrkpNYt/yLHgpDFxl8so=", "t+5tOOK8746DiCnoOgcm2tuNUAt65v8nfzbS3yEdi1I=", "yBSXVFCpmab6dinTWl6Oy1zeyHw1/GZzsRHtjw/aaYk=", "wcBd7XXR97yHapV5YPW2Drw8kk/CtZRXDp0VAAfjJHU=", "oPwDVN4FjVX2T6g6oI2rap2rOXxvnvk7PGdnBBviWX4="], "CachedAssets": {"Tetx7K/G6HNvYY0qGru6zJbdj2pYvGa0HJG0F5MzPqM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Appointmeent wasm\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\appsettings.Development.json", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Appointmeent wasm\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0ueugt8gp", "Integrity": "gX2wvy7Mp4NkxB2695Sb8lBM9HocPQ1U876BeP78Aws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.Development.json", "FileLength": 119, "LastWriteTime": "2025-06-27T04:50:09+00:00"}, "bY3TtkrwtO+XTdprzUAfv2yC0LcFoBLabc/TY7lypes=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Appointmeent wasm\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\appsettings.json", "SourceId": "AppointmentPlanner.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Appointmeent wasm\\AppointmentPlanner\\AppointmentPlanner.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0ueugt8gp", "Integrity": "gX2wvy7Mp4NkxB2695Sb8lBM9HocPQ1U876BeP78Aws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.json", "FileLength": 119, "LastWriteTime": "2025-06-27T04:50:09+00:00"}}, "CachedCopyCandidates": {}}