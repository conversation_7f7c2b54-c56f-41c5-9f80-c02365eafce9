@page "/reset-password"
@using AppointmentPlanner.Client.Models
@using AppointmentPlanner.Client.Services
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation

<div class="reset-password-container">
    <div class="reset-password-card">
        <div class="reset-password-header">
            <h2>Reset Your Password</h2>
            <p>Enter your new password below</p>
        </div>

        @if (!passwordReset)
        {
            <EditForm Model="@resetPasswordModel" OnValidSubmit="@HandleResetPassword">
                <DataAnnotationsValidator />
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <InputText id="email" @bind-Value="resetPasswordModel.Email" class="form-control" placeholder="Enter your email address" />
                    <ValidationMessage For="@(() => resetPasswordModel.Email)" />
                </div>

                <div class="form-group">
                    <label for="password">New Password</label>
                    <InputText id="password" @bind-Value="resetPasswordModel.Password" type="password" class="form-control" placeholder="Enter your new password" />
                    <ValidationMessage For="@(() => resetPasswordModel.Password)" />
                </div>

                <div class="form-group">
                    <label for="confirmPassword">Confirm New Password</label>
                    <InputText id="confirmPassword" @bind-Value="resetPasswordModel.ConfirmPassword" type="password" class="form-control" placeholder="Confirm your new password" />
                    <ValidationMessage For="@(() => resetPasswordModel.ConfirmPassword)" />
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger">
                        @errorMessage
                    </div>
                }

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span>Resetting Password...</span>
                        }
                        else
                        {
                            <span>Reset Password</span>
                        }
                    </button>
                </div>
            </EditForm>
        }
        else
        {
            <div class="success-message">
                <div class="success-icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22,4 12,14.01 9,11.01"></polyline>
                    </svg>
                </div>
                <h3>Password Reset Successfully</h3>
                <p>Your password has been reset successfully. You can now sign in with your new password.</p>
                
                <div class="form-group">
                    <button type="button" class="btn btn-primary btn-block" @onclick="@(() => Navigation.NavigateTo("/login"))">
                        Go to Sign In
                    </button>
                </div>
            </div>
        }

        <div class="reset-password-footer">
            <p>
                Remember your password? <a href="/login">Sign in here</a>
            </p>
        </div>
    </div>
</div>

<style>
    .reset-password-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .reset-password-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 450px;
    }

    .reset-password-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .reset-password-header h2 {
        color: #333;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .reset-password-header p {
        color: #666;
        margin: 0;
        line-height: 1.5;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 12px;
        border: 2px solid #e1e5e9;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .btn-block {
        width: 100%;
    }

    .alert {
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .success-message {
        text-align: center;
        padding: 20px 0;
    }

    .success-icon {
        color: #28a745;
        margin-bottom: 20px;
    }

    .success-message h3 {
        color: #333;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .success-message p {
        color: #666;
        margin-bottom: 25px;
        line-height: 1.5;
    }

    .reset-password-footer {
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e1e5e9;
    }

    .reset-password-footer p {
        margin: 10px 0;
    }

    .reset-password-footer a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
    }

    .reset-password-footer a:hover {
        text-decoration: underline;
    }

    .spinner-border {
        width: 1rem;
        height: 1rem;
        border: 0.125em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border 0.75s linear infinite;
    }

    .spinner-border-sm {
        width: 0.875rem;
        height: 0.875rem;
        border-width: 0.125em;
    }

    @keyframes spinner-border {
        to {
            transform: rotate(360deg);
        }
    }
</style>

@code {
    private ResetPasswordDto resetPasswordModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;
    private bool passwordReset = false;

    protected override void OnInitialized()
    {
        // Get query parameters from URL
        var uri = new Uri(Navigation.Uri);
        var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
        
        var email = query["email"];
        var token = query["token"];

        if (!string.IsNullOrEmpty(email))
        {
            resetPasswordModel.Email = email;
        }

        if (!string.IsNullOrEmpty(token))
        {
            resetPasswordModel.Token = token;
        }

        // If no token is provided, redirect to forgot password
        if (string.IsNullOrEmpty(resetPasswordModel.Token))
        {
            Navigation.NavigateTo("/forgot-password");
        }
    }

    private async Task HandleResetPassword()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;

            var result = await AuthService.ResetPasswordAsync(resetPasswordModel);

            if (result.IsSuccess)
            {
                passwordReset = true;
            }
            else
            {
                errorMessage = result.Message;
                if (result.Errors.Any())
                {
                    errorMessage += " " + string.Join(" ", result.Errors);
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An unexpected error occurred. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }
}
